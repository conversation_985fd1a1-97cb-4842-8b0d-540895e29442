name: AdaIR
channels:
  - nvidia
  - pytorch
  - https://mirrors.ustc.edu.cn/anaconda/pkgs/main
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=4.5=1_gnu
  - blas=1.0=mkl
  - bzip2=1.0.8=h7b6447c_0
  - ca-certificates=2022.12.7=ha878542_0
  - certifi=2022.12.7=pyhd8ed1ab_0
  - cpuonly=1.0=0
  - cuda=11.6.1=0
  - cuda-cccl=11.6.55=hf6102b2_0
  - cuda-command-line-tools=11.6.2=0
  - cuda-compiler=11.6.2=0
  - cuda-cudart=11.6.55=he381448_0
  - cuda-cudart-dev=11.6.55=h42ad0f4_0
  - cuda-cuobjdump=11.6.124=h2eeebcb_0
  - cuda-cupti=11.6.124=h86345e5_0
  - cuda-cuxxfilt=11.6.124=hecbf4f6_0
  - cuda-driver-dev=11.6.55=0
  - cuda-gdb=12.1.55=0
  - cuda-libraries=11.6.1=0
  - cuda-libraries-dev=11.6.1=0
  - cuda-memcheck=11.8.86=0
  - cuda-nsight=12.1.55=0
  - cuda-nsight-compute=12.1.0=0
  - cuda-nvcc=11.6.124=hbba6d2d_0
  - cuda-nvdisasm=12.1.55=0
  - cuda-nvml-dev=11.6.55=haa9ef22_0
  - cuda-nvprof=12.1.55=0
  - cuda-nvprune=11.6.124=he22ec0a_0
  - cuda-nvrtc=11.6.124=h020bade_0
  - cuda-nvrtc-dev=11.6.124=h249d397_0
  - cuda-nvtx=11.6.124=h0630a44_0
  - cuda-nvvp=12.1.55=0
  - cuda-runtime=11.6.1=0
  - cuda-samples=11.6.101=h8efea70_0
  - cuda-sanitizer-api=12.1.55=0
  - cuda-toolkit=11.6.1=0
  - cuda-tools=11.6.1=0
  - cuda-visual-tools=11.6.1=0
  - cudatoolkit=11.3.1=h2bc3f7f_2
  - ffmpeg=4.3=hf484d3e_0
  - freetype=2.10.4=h5ab3b9f_0
  - gds-tools=1.6.0.25=0
  - giflib=5.2.1=h7b6447c_0
  - gmp=6.2.1=h295c915_3
  - gnutls=3.6.15=he1e5248_0
  - intel-openmp=2021.3.0=h06a4308_3350
  - joblib=1.1.0=pyhd3eb1b0_0
  - jpeg=9b=h024ee3a_2
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.35.1=h7274673_9
  - libcublas=11.9.2.110=h5e84587_0
  - libcublas-dev=11.9.2.110=h5c901ab_0
  - libcufft=10.7.1.112=hf425ae0_0
  - libcufft-dev=10.7.1.112=ha5ce4c0_0
  - libcufile=1.6.0.25=0
  - libcufile-dev=1.6.0.25=0
  - libcurand=10.3.2.56=0
  - libcurand-dev=10.3.2.56=0
  - libcusolver=11.3.4.124=h33c3c4e_0
  - libcusparse=11.7.2.124=h7538f96_0
  - libcusparse-dev=11.7.2.124=hbbe9722_0
  - libffi=3.3=he6710b0_2
  - libgcc-ng=9.3.0=h5101ec6_17
  - libgfortran-ng=7.5.0=ha8ba4b0_17
  - libgfortran4=7.5.0=ha8ba4b0_17
  - libgomp=9.3.0=h5101ec6_17
  - libiconv=1.16=h7f8727e_2
  - libidn2=2.3.2=h7f8727e_0
  - libnpp=11.6.3.124=hd2722f0_0
  - libnpp-dev=11.6.3.124=h3c42840_0
  - libnvjpeg=11.6.2.124=hd473ad6_0
  - libnvjpeg-dev=11.6.2.124=hb5906b9_0
  - libpng=1.6.37=hbc83047_0
  - libstdcxx-ng=9.3.0=hd4cf53a_17
  - libtasn1=4.16.0=h27cfd23_0
  - libtiff=4.2.0=h85742a9_0
  - libunistring=0.9.10=h27cfd23_0
  - libuv=1.40.0=h7b6447c_0
  - libwebp=1.2.0=h89dd481_0
  - libwebp-base=1.2.0=h27cfd23_0
  - lz4-c=1.9.3=h295c915_1
  - mkl=2021.3.0=h06a4308_520
  - mkl-service=2.4.0=py38h7f8727e_0
  - mkl_fft=1.3.0=py38h42c9631_2
  - mkl_random=1.2.2=py38h51133e4_0
  - ncurses=6.2=he6710b0_1
  - nettle=3.7.3=hbbd107a_1
  - nsight-compute=2023.1.0.15=0
  - numpy=1.20.3=py38hf144106_0
  - numpy-base=1.20.3=py38h74d4b33_0
  - olefile=0.46=py_0
  - openh264=2.1.1=h4ff587b_0
  - openjpeg=2.3.0=h05c96fa_1
  - openssl=1.1.1k=h27cfd23_0
  - pip=21.0.1=py38h06a4308_0
  - python=3.8.11=h12debd9_0_cpython
  - pytorch-cuda=11.6=h867d48c_1
  - readline=8.1=h27cfd23_0
  - scikit-learn=1.0.1=py38h51133e4_0
  - scipy=1.6.2=py38had2a1c9_1
  - setuptools=52.0.0=py38h06a4308_0
  - six=1.16.0=pyhd3eb1b0_0
  - sqlite=3.36.0=hc218d9a_0
  - threadpoolctl=2.2.0=pyh0d69192_0
  - tk=8.6.10=hbc83047_0
  - torchaudio=0.8.1=py38
  - torchvision=0.9.1=py38_cpu
  - tqdm=4.62.0=pyhd3eb1b0_1
  - wheel=0.37.0=pyhd3eb1b0_0
  - xz=5.2.5=h7b6447c_0
  - zlib=1.2.11=h7b6447c_3
  - zstd=1.4.9=haebb681_0
  - pip:
      - accelerate==0.18.0
      - addict==2.4.0
      - aiohttp==3.8.4
      - aiosignal==1.3.1
      - anyio==3.6.2
      - appdirs==1.4.4
      - arrow==1.2.3
      - async-timeout==4.0.2
      - attrs==22.2.0
      - beautifulsoup4==4.12.1
      - blessed==1.20.0
      - charset-normalizer==3.0.1
      - click==8.1.3
      - cmake==3.26.1
      - contourpy==1.0.7
      - croniter==1.3.8
      - cycler==0.11.0
      - dataclasses==0.6
      - dateutils==0.6.12
      - deepdiff==6.3.0
      - deepspeed==0.8.3
      - dnspython==2.3.0
      - docker-pycreds==0.4.0
      - einops==0.6.0
      - email-validator==1.3.1
      - fastapi==0.88.0
      - filelock==3.9.0
      - fonttools==4.38.0
      - frozenlist==1.3.3
      - fsspec==2023.3.0
      - future==0.18.3
      - gitdb==4.0.10
      - gitpython==3.1.30
      - h11==0.14.0
      - hjson==3.1.0
      - httpcore==0.16.3
      - httptools==0.5.0
      - httpx==0.23.3
      - huggingface-hub==0.12.0
      - idna==3.4
      - imageio==2.25.0
      - inquirer==3.1.3
      - itsdangerous==2.1.2
      - jinja2==3.1.2
      - kiwisolver==1.4.4
      - lightning==2.0.1
      - lightning-cloud==0.5.32
      - lightning-utilities==0.8.0
      - lit==16.0.0
      - markdown-it-py==2.2.0
      - markupsafe==2.1.2
      - matplotlib==3.6.3
      - mdurl==0.1.2
      - mmcv==1.7.1
      - mmcv-full==1.7.1
      - mpmath==1.3.0
      - multidict==6.0.4
      - networkx==3.0
      - ninja==1.11.1
      - nvidia-cublas-cu11==11.10.3.66
      - nvidia-cuda-cupti-cu11==11.7.101
      - nvidia-cuda-nvrtc-cu11==11.7.99
      - nvidia-cuda-runtime-cu11==11.7.99
      - nvidia-cudnn-cu11==8.5.0.96
      - nvidia-cufft-cu11==10.9.0.58
      - nvidia-curand-cu11==10.2.10.91
      - nvidia-cusolver-cu11==11.4.0.1
      - nvidia-cusparse-cu11==11.7.4.91
      - nvidia-nccl-cu11==2.14.3
      - nvidia-nvtx-cu11==11.7.91
      - opencv-python==********
      - ordered-set==4.1.0
      - orjson==3.8.9
      - packaging==23.1
      - pandas==1.5.3
      - pathtools==0.1.2
      - pillow==9.4.0
      - protobuf==4.21.12
      - psutil==5.9.8
      - ptflops==*******
      - py-cpuinfo==9.0.0
      - pydantic==1.10.7
      - pygments==2.17.2
      - pyjwt==2.6.0
      - pyparsing==3.0.9
      - python-dateutil==2.8.2
      - python-dotenv==1.0.0
      - python-editor==1.0.4
      - python-multipart==0.0.6
      - pytorch-fid==0.3.0
      - pytorch-lightning==2.0.1
      - pytz==2022.7.1
      - pywavelets==1.4.1
      - pyyaml==6.0
      - readchar==4.0.5
      - requests==2.28.2
      - rfc3986==1.5.0
      - rich==13.3.3
      - scikit-image==0.19.3
      - scikit-video==1.1.11
      - seaborn==0.12.2
      - sentry-sdk==1.14.0
      - setproctitle==1.3.2
      - smmap==5.0.0
      - sniffio==1.3.0
      - soupsieve==2.4
      - starlette==0.22.0
      - starsessions==1.3.0
      - sympy==1.11.1
      - tifffile==2023.1.23.1
      - timm==0.6.12
      - torch==1.13.1
      - torchmetrics==0.11.4
      - torchsummary==1.5.1
      - traitlets==5.14.1
      - triton==2.0.0
      - tsnecuda==3.0.1
      - typing-extensions==4.5.0
      - ujson==5.7.0
      - urllib3==1.26.14
      - uvicorn==0.21.1
      - uvloop==0.17.0
      - wandb==0.13.9
      - watchfiles==0.19.0
      - wcwidth==0.2.13
      - websocket-client==1.5.1
      - websockets==11.0.1
      - yapf==0.32.0
      - yarl==1.8.2
